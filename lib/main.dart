import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:get_storage/get_storage.dart';
import 'package:idea2app_vendor_app/firebase_options.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/injector/injector.dart'
    as injector;
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/screens/shared/base_multi_provider/base_multi_provider.dart';
import 'package:idea2app_vendor_app/src/screens/splash_screen/view/splash_screen.dart';
import 'package:path_provider/path_provider.dart';
import 'package:url_strategy/url_strategy.dart';

//! git commit --amend --author="Idea2App <<EMAIL>>"
//! GIT_SSH_COMMAND="ssh -i ~/.ssh/id_rsa_idea2app" git push --force origin main

Future main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Gemini.init(apiKey: AppConsts.geminiApiKey, enableDebugging: kDebugMode);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  if (kIsWeb) {
    setPathUrlStrategy();
  }

  await GetStorage.init();

  String storageLocation =
      kIsWeb ? '' : (await getApplicationDocumentsDirectory()).path;

  // if (kReleaseMode || (kDebugMode && !UniversalPlatform.isApple)) {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  // }

  await Future.wait([
    FastCachedImageConfig.init(subDir: storageLocation),
    injector.appInjector(),
  ]);

  // if (kReleaseMode || (kDebugMode && !UniversalPlatform.isApple)) {
  //   NotificationService.init();
  // }

  runApp(Phoenix(child: const BaseMultiProvider(child: BuildAppView())));
}

class BuildAppView extends StatelessWidget {
  const BuildAppView({super.key});

  @override
  Widget build(BuildContext context) {
    bool isDarkMode() {
      final brightness =
          SchedulerBinding.instance.platformDispatcher.platformBrightness;
      return brightness == Brightness.dark;
    }

    final isDarkTheme = context.isDark || isDarkMode();

    Widget buildRunnableApp({
      required double webAppWidth,
      required Widget app,
    }) {
      if (!kIsWeb) {
        return app;
      }

      return Directionality(
        textDirection: TextDirection.ltr,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Container(
              color: isDarkTheme
                  ? ColorManager.darkBackgroundColor
                  : ColorManager.backgroundColor,
              width: double.infinity,
              height: double.infinity,
            ),
            Center(
              child: ClipRect(
                child: SizedBox(
                  width: webAppWidth,
                  child: app,
                ),
              ),
            ),
          ],
        ),
      );
    }

    final runnableApp = buildRunnableApp(
      webAppWidth: 800.0,
      app: const SplashScreen(),
    );

    return runnableApp;
  }
}
