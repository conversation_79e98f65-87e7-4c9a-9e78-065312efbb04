import 'dart:developer';

import 'package:animated_splash_screen/animated_splash_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme_manager.dart';
import 'package:idea2app_vendor_app/src/screens/check_update_screen.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/app_settings/view_model/app_settings_view_model.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:universal_platform/universal_platform.dart';

import '../../../../generated/assets.dart';
import '../../../app.dart';
import '../../../core/resources/theme/theme.dart';
import '../../../core/services/default_country_service.dart';
import '../../../core/services/local_notifications_service.dart';
import '../../../core/services/notifications_service.dart';
import '../../../core/utils/logger.dart';
import '../../auth/models/helper_models/vendor_helper_model.dart';
import '../../auth/models/vendor_model.dart';
import '../../auth/view/login/login.screen.dart';
import '../../auth/view_model/auth_view_model.dart';
import '../../banner/view_model/banner_VM.dart';
import '../../settings/view_model/settings_view_model.dart';

class SplashScreen extends HookWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);

    final orderVM = context.read<OrderVM>();
    final bannerVM = context.read<BannerVM>();

    return Consumer2<AppSettingsVM, SettingsVM>(
      builder: (context, appSettingsVM, settingsVM, child) {
        return MaterialApp(
          navigatorKey: mainScreenNavigatorKey,
          debugShowCheckedModeBanner: false,
          debugShowMaterialGrid: false,
          title: AppConsts.appName,
          //? Theme
          themeMode: appSettingsVM.themeMode,
          themeAnimationCurve: Curves.easeInOutSine,
          themeAnimationDuration: const Duration(milliseconds: 250),
          theme: AppTheme.lightTheme(context),
          darkTheme: AppTheme.darkTheme(context),
          //? Localization
          supportedLocales: AppConsts.supportedLocales,
          locale: appSettingsVM.locale,
          localizationsDelegates: AppConsts.localizationsDelegates,
          builder: (context, child) {
            return SafeArea(
              top: false,
              left: false,
              right: false,
              bottom: true,
              child: child!,
            );
          },
          home: AnimatedSplashScreen.withScreenFunction(
              animationDuration: const Duration(seconds: 1),
              splash: Column(
                children: [
                  Image.asset(Assets.imagesSplashLogo,
                      width: context.isBigScreen ? 220 : 190.w),
                  context.largeGap,
                  SizedBox(
                      width: 100,
                      child: LoadingAnimationWidget.threeArchedCircle(
                          color: ColorManager.primaryColor, size: 45))
                ],
              ),
              screenFunction: () async {
                try {
                  await settingsVM.getSettings(context);
                  await settingsVM.getAppVersion();
                } catch (e, s) {
                  Log.w('getSettingsError: $e $s');
                }

                VendorModel? vendor;

                try {
                  vendor =
                      await context.read<AuthVM>().getCurrentVendor(context);

                  if (vendor?.isFree == true) {
                    bannerVM.getVendorBannersData(context);
                  }

                  if (vendor != null) {
                    if (kIsWeb ||
                        kReleaseMode ||
                        (kDebugMode && !UniversalPlatform.isApple)) {
                      log('asfdsaffffsaf ');
                      NotificationService.init();
                    }

                    final isAdmin = VendorModelHelper.isAdmin();

                    if (isAdmin) {
                      LocalNotificationService.subscribeForAdmin(context);
                    } else {
                      LocalNotificationService.subscribeForVendor(context);
                    }

                    orderVM.getOrderStatistics(context);
                    orderVM.getHomeOrders(context);
                  } else {
                    DefaultCountryService.getDefaultCurrency();

                    return const LoginScreen();
                  }
                } catch (e) {
                  return const LoginScreen();
                }

                final checkUpdate = settingsVM.settings?.checkUpdate == true;

                if (checkUpdate && !kIsWeb) {
                  return const CheckUpdateWidget();
                }

                return const SelectedScreen();
              },
              splashIconSize: 300,
              splashTransition: SplashTransition.fadeTransition,
              pageTransitionType: PageTransitionType.bottomToTop,
              backgroundColor: ColorManager.black),
        );
      },
    );
  }
}
