import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/common/base_safe_area.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/login/login.screen.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/register/widgets/register_body.widget.dart';
import 'package:idea2app_vendor_app/src/screens/drawer/app_drawer.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';

class RegisterScreen extends HookWidget {
  const RegisterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const LoginScreen());
        context.read<MediaVM>().clearFiles();
        return true;
      },
      child: const Scaffold(
        drawer: AppDrawer(),
        body: BaseSafeArea(
          withSpaceForIOS: true,
          child: RegisterB<PERSON>(),
        ),
      ),
    );
  }
}
