import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/animation_extensions.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/services/default_country_service.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/login/login.screen.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/register/widgets/register_fields.widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/register/widgets/register_top_section.widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/country/view_model/country_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/currency/model/currency_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/settings_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';
import 'package:universal_platform/universal_platform.dart';

import '../../../../../core/services/notifications_service.dart';
import '../../../../banner/view_model/banner_VM.dart';
import '../../../../country/model/country_model.dart';
import '../../../../currency/view_model/currency_view_model.dart';
import 'choose_country.widget.dart';
import 'choose_currency.widget.dart';

class RegisterBody extends HookWidget {
  const RegisterBody({super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.read<AuthVM>();

    final currencyVM = context.read<CurrencyVM>();
    final countryVM = context.read<CountryVM>();

    final selectedBusinessType = useState<TemplateModel?>(null);

    final defaultCountry = countryVM.countries?.firstWhereOrNull(
        (element) => element.name == DefaultCountryService.currentCountry);

    final defaultCurrency = currencyVM.currencies?.firstWhereOrNull((element) =>
        element.currencyEn == DefaultCountryService.currentCurrency);

    final selectedCountryModel = useState<CountryModel?>(defaultCountry);
    final selectedCurrencyModel = useState<CurrencyModel?>(defaultCurrency);

    final controllers = {
      ApiStrings.name: useTextEditingController(),
      ApiStrings.websiteName: useTextEditingController(),
      ApiStrings.phone: useTextEditingController(),
      ApiStrings.email: useTextEditingController(),
      ApiStrings.password: useTextEditingController(),
    };

    final formKey = useState(GlobalKey<FormState>());

    Future<void> registerVendor() async {
      if (!formKey.value.currentState!.validate()) {
        return;
      }

      final mediaVM = context.read<MediaVM>();
      final formatted = controllers[ApiStrings.websiteName]
          ?.text
          .toLowerCase()
          .replaceAll(' ', '-')
          .replaceAll(RegExp(r'[^a-zA-Z0-9-_]'), '');
      controllers[ApiStrings.websiteName]?.text = formatted ?? '';

      await authVM.registerVendor(context,
          controllers: controllers,
          selectedBusinessType: selectedBusinessType.value,
          pickedImage: mediaVM.filesPaths.firstOrNull ?? '',
          countryId: selectedCountryModel.value?.id ?? 0,
          currencyId: selectedCurrencyModel.value?.id ?? 0,
          cities: selectedCountryModel.value?.cities ?? []);
    }

    return Form(
      key: formKey.value,
      child: Center(
        child: Consumer<AuthVM>(
          builder: (context, authVM, child) {
            return ListView(
              padding: const EdgeInsets.only(
                left: AppSpaces.mediumPadding,
                right: AppSpaces.mediumPadding,
                bottom: AppSpaces.mediumPadding,
              ),
              children: [
                const RegisterTopSection(),

                context.mediumGap,

                RegisterFields(
                  controllers: controllers,
                  selectedBusinessType: selectedBusinessType,
                ),

                context.largeGap,
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: InkWell(
                        onTap: () {
                          showModalBottomSheet(
                            isScrollControlled: true,
                            enableDrag: true,
                            backgroundColor:
                                context.appTheme.scaffoldBackgroundColor,
                            context: context,
                            builder: (context) {
                              return ChooseCountryWidget(
                                countries: countryVM.countries,
                                selectedCountry: selectedCountryModel,
                                onSelected: (country) {
                                  const countryCurrencyMap = {
                                    'Egypt': 'EGP',
                                    'Saudi Arabia': 'SAR',
                                    'United Arab Emirates': 'AED',
                                  };

                                  final currencyCode =
                                      countryCurrencyMap[country.name] ?? 'EGP';

                                  selectedCurrencyModel.value = currencyVM
                                      .currencies
                                      ?.firstWhereOrNull((element) =>
                                          element.currencyEn == currencyCode);
                                },
                              );
                            },
                          );
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              context.tr.selectCountry,
                              style: context.labelLarge,
                            ),
                            context.smallGap,
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpaces.mediumPadding,
                                vertical: AppSpaces.smallPadding + 4,
                              ),
                              decoration: BoxDecoration(
                                color: ColorManager.textFieldColor(context),
                                borderRadius: BorderRadius.circular(
                                  AppRadius.baseRadius,
                                ),
                              ),
                              child: Row(
                                children: [
                                  SizedBox(
                                    height: 25,
                                    width: 35,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(
                                        AppRadius.smallRadius,
                                      ),
                                      child: SvgPicture.network(
                                        selectedCountryModel.value?.icon?.url ??
                                            '',
                                        height: 30,
                                        width: 40,
                                      ),
                                    ),
                                  ),
                                  context.mediumGap,
                                  Expanded(
                                    child: Text(
                                      countryVM.getCountryNameByLang(context,
                                              country:
                                                  selectedCountryModel.value ??
                                                      CountryModel()) ??
                                          '',
                                      style: context.labelMedium,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    context.smallGap,
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          showModalBottomSheet(
                            isScrollControlled: true,
                            enableDrag: true,
                            backgroundColor:
                                context.appTheme.scaffoldBackgroundColor,
                            context: context,
                            builder: (context) {
                              return ChooseCurrencyWidget(
                                currencies: currencyVM.currencies,
                                selectedCurrency: selectedCurrencyModel,
                              );
                            },
                          );
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              context.tr.selectCurrency,
                              style: context.labelLarge,
                            ),
                            context.smallGap,
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpaces.mediumPadding,
                                vertical: AppSpaces.smallPadding + 4,
                              ),
                              decoration: BoxDecoration(
                                color: ColorManager.textFieldColor(context),
                                borderRadius: BorderRadius.circular(
                                  AppRadius.baseRadius,
                                ),
                              ),
                              child: Row(
                                children: [
                                  SizedBox(
                                    height: 25,
                                    width: 35,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(
                                        AppRadius.smallRadius,
                                      ),
                                      child: SvgPicture.network(
                                        selectedCurrencyModel
                                                .value?.icon?.url ??
                                            '',
                                        height: 30,
                                        width: 40,
                                      ),
                                    ),
                                  ),
                                  context.mediumGap,
                                  Expanded(
                                    child: Text(
                                      currencyVM.getCurrencyNameByLang(context,
                                              country: selectedCurrencyModel
                                                  .value!) ??
                                          '',
                                      style: context.labelMedium,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                context.largeGap,

                Selector<AuthVM, bool>(
                  selector: (context, provider) => provider.isLoading,
                  builder: (context, isLoading, child) {
                    return Button(
                      isLoading: isLoading,
                      radius: AppRadius.extraLargeContainerRadius,
                      onPressed: () async {
                        await registerVendor();
                        context.read<BannerVM>().getVendorBannersData(context);

                        if (kIsWeb ||
                            kReleaseMode ||
                            (kDebugMode && !UniversalPlatform.isApple)) {
                          NotificationService.init();
                        }
                      },
                      label: context.tr.register,
                    );
                  },
                ).bottomSlide,

                context.smallGap,

                //! Don't have an account
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(context.tr.iHaveAnAccount, style: context.labelMedium),
                    InkWell(
                      borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                      onTap: () {
                        context.to(const LoginScreen());
                        context.read<MediaVM>().clearFiles();
                      },
                      child: Text(
                        context.tr.login,
                        style: context.labelMedium.copyWith(
                          color: ColorManager.primaryColor,
                          decoration: TextDecoration.underline,
                          decorationColor: ColorManager.primaryColor,
                        ),
                      ).paddingAll(AppSpaces.xSmallPadding),
                    ),
                  ],
                ).bottomSlide,
              ],
            );
          },
        ),
      ),
    );
  }
}
