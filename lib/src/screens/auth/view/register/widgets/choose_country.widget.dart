import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/country/model/country_model.dart';
import 'package:idea2app_vendor_app/src/screens/country/view_model/country_view_model.dart';
import 'package:provider/provider.dart';

class ChooseCountryWidget extends StatelessWidget {
  final List<CountryModel>? countries;
  final ValueNotifier<CountryModel?> selectedCountry;
  final Function(CountryModel)? onSelected;

  const ChooseCountryWidget({
    super.key,
    required this.countries,
    required this.selectedCountry,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpaces.mediumPadding,
        vertical: AppSpaces.largePadding,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            context.tr.selectCountry,
            style: context.headLine.copyWith(color: ColorManager.primaryColor),
          ),
          context.smallGap,
          Text(
            context.tr.selectYourCountryForTheAppsYouCanChangedLatter,
            textAlign: TextAlign.center,
            style: context.labelMedium.copyWith(
              color: context.appTheme.primaryColorDark.withAlpha(150),
              fontWeight: FontWeight.w400,
            ),
          ),
          context.largeGap,
          ValueListenableBuilder<CountryModel?>(
            valueListenable: selectedCountry,
            builder: (context, value, child) {
              return Column(
                children: countries?.map((country) {
                      final isSelected =
                          value?.documentId == country.documentId;
                      return GestureDetector(
                        onTap: () {
                          selectedCountry.value = country;

                          if (onSelected != null) {
                            onSelected!(country);
                          }

                          context.back();
                        },
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.symmetric(
                              horizontal: AppSpaces.mediumPadding,
                              vertical: AppSpaces.smallPadding + 4),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? ColorManager.primaryColor.withAlpha(30)
                                : null,
                            borderRadius:
                                BorderRadius.circular(AppRadius.baseRadius),
                            border: Border.all(
                              color: isSelected
                                  ? ColorManager.primaryColor
                                  : context.appTheme.primaryColorDark
                                      .withAlpha(30),
                              width: 1.8,
                            ),
                          ),
                          child: Row(
                            children: [
                              SizedBox(
                                height: 30,
                                width: 40,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                      AppRadius.smallRadius),
                                  child: SvgPicture.network(
                                    country.icon?.url ?? '',
                                    height: 30,
                                    width: 40,
                                  ),
                                ),
                              ),
                              context.mediumGap,
                              Expanded(
                                child: Text(
                                  context
                                          .read<CountryVM>()
                                          .getCountryNameByLang(context,
                                              country: country) ??
                                      '',
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500),
                                ),
                              ),
                              Icon(
                                isSelected
                                    ? Icons.radio_button_checked
                                    : Icons.radio_button_off,
                                color: context.isDark
                                    ? Colors.white
                                    : Colors.black,
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList() ??
                    [],
              );
            },
          ),
        ],
      ),
    );
  }
}
