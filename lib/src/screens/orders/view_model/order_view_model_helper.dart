import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/services/notifications_service.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/invoices/view/store_invoices_screen.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
import 'package:idea2app_vendor_app/src/screens/orders/repository/order_repository.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:provider/provider.dart';

import '../../dashboard/models/extra_setting_model.dart';
import '../models/products_quantity_model.dart';

class OrderVMHelper extends BaseVM {
  final OrderRepo _orderRepo;

  OrderVMHelper(this._orderRepo);

  //! Send Notification For Delivery Cost ================================
  Future<void> sendDeliveryCost(BuildContext context,
      {required OrderModel order}) async {
    await baseFunction(context, () async {
      final title = context.tr.yourDeliveryCostHasBeenChanged;

      final body = context.tr.deliveryCostChangedTo(order.deliveryCost!);

      await NotificationService.sendNotification(
        title: title,
        body: body,
        userTokenOrTopic: order.deviceToken ?? order.user?.deviceToken ?? '',
      );
    });
  }

  //! Send Notification For Each Status ================================
  Future<void> sendOrderStatus(BuildContext context,
      {required OrderModel order}) async {
    await baseFunction(context, () async {
      final title = context.tr.yourOrderStatusHasBeenChanged;

      final status = order.status!.getTranslation(context);

      final body = context.tr.orderStatusChangedTo(status);

      await NotificationService.sendNotification(
        title: title,
        body: body,
        userTokenOrTopic: order.deviceToken ?? order.user?.deviceToken ?? '',
      );
    });
  }

  List<OrderModel> selectedOrders = [];

  void onSelectedOrder(OrderModel order) {
    if (selectedOrders.contains(order)) {
      selectedOrders.remove(order);
    } else {
      selectedOrders.add(order);
    }
    notifyListeners();
  }

  //! Call Customer ================================
  Future<void> callCustomer(BuildContext context,
      {required String? phone}) async {
    Log.f("Call Phone: $phone");

    await baseFunction(context, () async {
      await _orderRepo.callCustomer(phone: phone);
    });
  }

  //! Open Whatsapp ================================
  Future<void> openWhatsApp(BuildContext context,
      {required String phone}) async {
    Log.f("OpenWhatsApp: $phone");

    await baseFunction(context, () async {
      await _orderRepo.openWhatsApp(phone: phone);
    });
  }

  //! Print Order ================================
  Future<String> generateStoreOrderInvoice(BuildContext context,
      {required OrderModel order}) async {
    return await baseFunction(
      context,
      () async {
        final generatedPDFReportPath =
            await _orderRepo.generateStoreOrderInvoice(
                invoicesScaffoldKey.currentContext ?? context,
                order: order);

        return generatedPDFReportPath;
      },
      onError: (e, s) async {
        final generatedPDFReportPath =
            await _orderRepo.generateStoreOrderInvoice(context, order: order);

        return generatedPDFReportPath;
      },
    );
  }

  //! Print Order ================================
  Future<String> generateOnlineOrderInvoice(BuildContext context,
      {required OrderModel order}) async {
    return await baseFunction(context, () async {
      final generatedPDFReportPath =
          await _orderRepo.generateOnlineOrderInvoice(context, order: order);

      return generatedPDFReportPath;
    });
  }

  Future<void> decreaseStockQuantity(BuildContext context,
      {required List<ProductQuantityModel> products}) async {
    await baseFunction(context, () async {
      final productVM = context.read<ProductVM>();
      final productsList = await productVM.getAllProducts(context);

      for (var productQuantity in products) {
        final product = productsList.firstWhereOrNull((element) =>
            element.documentId == productQuantity.product?.documentId);

        if (product == null ||
            product.inventory == null ||
            (product.inventory != null && product.inventory! <= 0)) {
          continue;
        }

        var inventory = productQuantity.product?.inventory ?? 0;

        if (canDecreaseStock(product.colors, productQuantity.color)) {
          inventory -= productQuantity.quantity!;
        }
        if (canDecreaseStock(product.sizes, productQuantity.size)) {
          inventory -= productQuantity.quantity!;
        }

        final updatedColors = updateStock(
            product.colors, productQuantity.color, productQuantity.quantity!);
        final updatedSizes = updateStock(
            product.sizes, productQuantity.size, productQuantity.quantity!);

        context.read<ProductVM>().updateInventory(context,
            productId: product.documentId!,
            inventory: inventory >= 0 ? inventory : 0,
            colors: updatedColors,
            sizes: updatedSizes);
      }
    });
  }

  bool canDecreaseStock(
      List<ExtraSettingsModel> variants, String? variantName) {
    return variants
        .where((element) =>
            element.englishName == variantName && element.stock! > 0)
        .isNotEmpty;
  }

  Future<void> increaseStockQuantity(BuildContext context,
      {required List<ProductQuantityModel> products}) async {
    await baseFunction(context, () async {
      final productVM = context.read<ProductVM>();

      final productsList = await productVM.getAllProducts(context);

      for (var productQuantity in products) {
        if (productQuantity.product?.inventory != null &&
            (productQuantity.product!.inventory != null &&
                productQuantity.product!.inventory! >= 0)) {
          final product = productsList.firstWhere((element) =>
              element.documentId == productQuantity.product!.documentId);

          var inventory = productQuantity.product?.inventory ?? 0;

          final canAddToColorStock = product.colors
              .where((element) => element.englishName == productQuantity.color)
              .isNotEmpty;

          final canAddToSizeStock = product.sizes
              .where((element) => element.englishName == productQuantity.size)
              .isNotEmpty;

          if (canAddToColorStock) {
            inventory += productQuantity.quantity!;
          }
          if (canAddToSizeStock) {
            inventory += productQuantity.quantity!;
          }

          final updatedColor = product.colors
              .firstWhereOrNull(
                (element) => element.englishName == productQuantity.color,
              )
              ?.copyWith(
                  stock: product.colors
                          .firstWhere((element) =>
                              element.englishName == productQuantity.color)
                          .stock! +
                      productQuantity.quantity!);

          final updatedSize = product.sizes
              .firstWhereOrNull(
                (element) => element.englishName == productQuantity.size,
              )
              ?.copyWith(
                  stock: product.sizes
                          .firstWhere((element) =>
                              element.englishName == productQuantity.size)
                          .stock! +
                      productQuantity.quantity!);

          final updatedColors = product.colors
              .map((e) =>
                  e.englishName == productQuantity.color ? updatedColor : e)
              .toList();

          final updatedSizes = product.sizes
              .map((e) =>
                  e.englishName == productQuantity.size ? updatedSize : e)
              .toList();

          context.read<ProductVM>().updateInventory(context,
              productId: productQuantity.product!.documentId!,
              inventory: inventory,
              colors: updatedColors,
              sizes: updatedSizes);
        }
      }
    });
  }

  List<ExtraSettingsModel> updateStock(
      List<ExtraSettingsModel> variants, String? variantName, int quantity) {
    return variants
        .map((variant) => variant.englishName == variantName
            ? variant.copyWith(stock: variant.stock! - quantity)
            : variant)
        .toList();
  }
}
