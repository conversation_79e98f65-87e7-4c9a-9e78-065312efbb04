import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/settings_model.dart';
import 'package:provider/provider.dart';

import '../../../core/consts/api_strings.dart';
import '../../../core/shared_widgets/dialogs/alert_dialog.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../shared/media/view_models/media_view_model.dart';
import 'widgets/edit_profile_fields.dart';

class EditProfileScreen extends HookWidget {
  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.read<AuthVM>();

    final selectedBusinessType = useState<TemplateModel?>(TemplateModel(
      name: authVM.currentVendor?.businessType ?? '',
    ));

    final controllers = {
      ApiStrings.name:
          useTextEditingController(text: authVM.currentVendor?.name),
      ApiStrings.address:
          useTextEditingController(text: authVM.currentVendor?.address),
      ApiStrings.phone:
          useTextEditingController(text: authVM.currentVendor?.phone),
      ApiStrings.email:
          useTextEditingController(text: authVM.currentVendor?.email),
      ApiStrings.password: useTextEditingController(),
    };

    final formKey = useState(GlobalKey<FormState>());

    Future<void> editVendor() async {
      if (!formKey.value.currentState!.validate()) return;
      final mediaVM = context.read<MediaVM>();

      await authVM.editVendor(context,
          controllers: controllers,
          documentId: authVM.currentVendor?.documentId,
          selectedBusinessType: selectedBusinessType.value,
          pickedImage: mediaVM.filesPaths.firstOrNull ?? '');
    }

    return Scaffold(
      appBar: MainAppBar(
        haveBackButton: true,
        // title: context.tr.editProfile,

        actionWidget: VendorModelHelper.isParentVendor()
            ? TextButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      // final isLoading =
                      //     context.watch<OrderVM>().isLoading;

                      return AlertDialogWidget(
                        isLoading: false,
                        isWarningMessage: true,
                        child: Text(
                            context.tr.areYouSureYouWantToDeleteYourAccount,
                            style: context.labelLarge),
                        onConfirm: () async {
                          await authVM.deleteVendor(context,
                              vendorId: authVM.currentVendor?.id);
                        },
                      );
                    },
                  );
                },
                child: Text(
                  context.tr.deleteAccount,
                  style: context.hint.copyWith(color: Colors.red, fontSize: 12),
                ),
              )
            : const SizedBox(),
      ),
      body: Form(
        key: formKey.value,
        child: ListView(
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          children: [
            EditProfileFields(
                controllers: controllers,
                selectedBusinessType: selectedBusinessType),
            context.xLargeGap,
            Selector<AuthVM, bool>(
              selector: (context, provider) => provider.isLoading,
              builder: (context, isLoading, child) {
                return Button(
                  isLoading: isLoading,
                  onPressed: () async => await editVendor(),
                  label: context.tr.save,
                ).paddingOnly(bottom: 20);
              },
            )
          ],
        ),
      ),
    );
  }
}
