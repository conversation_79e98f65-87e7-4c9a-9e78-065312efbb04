import 'dart:developer';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view_model/vendors_view_model.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:provider/provider.dart';

class LocalNotificationService {
  static void subscribeForAdmin(
    BuildContext context,
  ) async {
    final vendorsVM = context.read<VendorsVM>();

    final vendors = await vendorsVM.getVendors(context);

    //! Initialize notifications
    AwesomeNotifications().initialize(
      'resource://drawable/ic_stat_onesignal_default',
      // 'resource://drawable/ic_launcher_background',
      [
        NotificationChannel(
          channelKey: 'basic_channel',
          channelName: 'Important notifications',
          channelDescription:
              'Notification channel for important notifications',
          playSound: true,
          enableVibration: true,
          importance: NotificationImportance.High,
        ),
      ],
    );

    await AwesomeNotifications()
        .isNotificationAllowed()
        .then((isAllowed) async {
      if (!isAllowed) {
        await AwesomeNotifications().requestPermissionToSendNotifications();
      }
    });

    // Schedule notifications for each vendor
    for (var vendor in vendors) {
      if (vendor.expireDate != null && vendor.pricing != null) {
        final notificationDate =
            vendor.expireDate!.subtract(const Duration(days: 1));

        AwesomeNotifications().createNotification(
          content: NotificationContent(
            id: vendor.id ?? 0,
            channelKey: 'basic_channel',
            title: context.readIsEng
                ? "${vendor.name} (${vendor.vendorType?.name}) Subscription Reminder"
                : "تذكير بالاشتراك (${vendor.vendorType?.name}) لـ ${vendor.name}",
            body: context.readIsEng
                ? "Expires on ${vendor.expireDate?.formatDayName}, ${vendor.expireDate?.formatDateToString}<br>Subscription Amount: ${vendor.pricing?.price} ${vendor.config?.currencies.firstOrNull?.currencyEn ?? 'EGP'}"
                : "ينتهي في ${vendor.expireDate?.formatDayName}, ${vendor.expireDate?.formatDateToString}<br>مبلغ الاشتراك: ${vendor.pricing?.price} ${vendor.config?.currencies.firstOrNull?.currencyAr ?? 'ج.م'}",
            notificationLayout: NotificationLayout.BigPicture,
            bigPicture: 'asset://assets/images/logo.jpeg',
          ),
          schedule: NotificationCalendar(
            year: notificationDate.year,
            month: notificationDate.month,
            day: notificationDate.day,
            hour: 20,
            minute: 0,
            second: 0,
            millisecond: 0,
            allowWhileIdle: true,
            repeats: false,
          ),
        );
      }
    }
  }

  static void subscribeForVendor(
    BuildContext context,
  ) async {
    final vendorVM = context.read<AuthVM>();

    final vendor = vendorVM.currentVendor;

    log('asffasff ${vendor?.businessName}');

    //! Initialize notifications
    AwesomeNotifications().initialize(
      'resource://drawable/ic_stat_onesignal_default',
      [
        NotificationChannel(
          channelKey: 'basic_channel',
          channelName: 'Important notifications',
          channelDescription:
              'Notification channel for important notifications',
          playSound: true,
          enableVibration: true,
          importance: NotificationImportance.High,
        ),
      ],
    );

    await AwesomeNotifications()
        .isNotificationAllowed()
        .then((isAllowed) async {
      if (!isAllowed) {
        await AwesomeNotifications().requestPermissionToSendNotifications();
      }
    });

    if (vendor?.expireDate != null && vendor?.pricing != null) {
      final notificationDate =
          vendor?.expireDate!.subtract(const Duration(days: 1));

      AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: vendor?.id ?? 0,
          channelKey: 'basic_channel',
          title:
              context.readIsEng ? "Subscription Reminder" : "تذكير بالاشتراك",
          body: context.readIsEng
              ? "Expires on ${vendor?.expireDate?.formatDayName}, ${vendor?.expireDate?.formatDateToString}<br>Subscription Amount: ${vendor?.pricing?.price} ${vendor?.config?.currencies.firstOrNull?.currencyEn ?? 'EGP'}"
              : "ينتهي في ${vendor?.expireDate?.formatDayName}, ${vendor?.expireDate?.formatDateToString}<br>مبلغ الاشتراك: ${vendor?.pricing?.price} ${vendor?.config?.currencies.firstOrNull?.currencyAr ?? 'ج.م'}",
          notificationLayout: NotificationLayout.BigPicture,
          bigPicture: 'asset://assets/images/logo.jpeg',
        ),
        schedule: NotificationCalendar(
          year: notificationDate?.year,
          month: notificationDate?.month,
          day: notificationDate?.day,
          hour: 20,
          minute: 0,
          second: 0,
          millisecond: 0,
          allowWhileIdle: true,
          repeats: false,
        ),
      );
    }
  }
}
