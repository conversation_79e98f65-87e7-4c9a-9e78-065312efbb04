import 'dart:convert';
import 'dart:developer';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:http/http.dart' as http;
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:universal_platform/universal_platform.dart';

import '../../app.dart';
import '../../screens/orders/models/order/order_model.dart';
import '../../screens/orders/view/order_details_screen/order_details_screen.dart';

class NotificationService {
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    if (Firebase.apps.isEmpty) await Firebase.initializeApp();
    log('RRRR ${VendorModelHelper.currentVendor().isEmpty}');

    if (VendorModelHelper.currentVendor().isEmpty) return;

    _showAwesomeNotification(message);
  }

  static Future<void> init() async {
    final fcm = FirebaseMessaging.instance;

    log('Is_Order_Ring_Bell_Opened: ${VendorModelHelper.currentVendorModel().config?.orderRingingBell}');

    await AwesomeNotifications().requestPermissionToSendNotifications();

    // Initialize Awesome Notifications
    AwesomeNotifications().initialize(
      null,
      VendorModelHelper.currentVendorModel().config?.orderRingingBell == true
          ? [
              NotificationChannel(
                channelKey: 'sound_channel',
                channelName: 'Basic Notifications',
                channelDescription:
                    'Notification channel for basic notifications',
                importance: NotificationImportance.Max,
                soundSource: 'resource://raw/bell',
                playSound: true,
              )
            ]
          : [
              NotificationChannel(
                channelKey: 'basic_channel',
                channelName: 'Basic Notifications',
                channelDescription:
                    'Notification channel for basic notifications',
                importance: NotificationImportance.Max,
                defaultRingtoneType: DefaultRingtoneType.Notification,
              )
            ],
    );

    // Request permission for notifications
    await fcm.requestPermission(
      alert: true,
      badge: true,
      provisional: false,
      sound: true,
      criticalAlert: true,
    );

    // Set the options for notifications when the app is in the foreground
    fcm.setForegroundNotificationPresentationOptions(
      badge: true,
      alert: true,
      sound: true,
    );

    // Set up Firebase Messaging background message handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    if (kIsWeb) return;

    fcm.subscribeToTopic('vendors');
  }

  static void _handleNotification(RemoteMessage message,
      {required OrderModel order}) {
    final isOrder =
        (message.notification?.title?.contains('new order') ?? false) ||
            (message.notification?.title?.contains('طلب جديد') ?? false);

    Log.w('isOrder: $isOrder');

    _showAwesomeNotification(message);

    if (isOrder) {
      _showOrderDialog(message, order: order);
    }
  }

  static void _showOrderDialog(RemoteMessage message,
      {required OrderModel order}) {
    final context = mainScreenNavigatorKey.currentContext!;

    QuickAlert.show(
        context: context,
        type: QuickAlertType.success,
        title: '',
        borderRadius: 30,
        disableBackBtn: false,
        showConfirmBtn: false,
        showCancelBtn: false,
        backgroundColor: context.appTheme.cardColor,
        widget: Column(
          children: [
            Text(context.tr.youHaveNewOrder, style: context.title),
            context.smallGap,
            Text('${context.tr.orderId} #${order.orderId}',
                style: context.labelLarge),
            context.smallGap,
            Text(context.tr.youHaveNewOrderPleaseCheckYourLatestOrders,
                textAlign: TextAlign.center, style: context.labelMedium),
            context.largeGap,
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      height: 35.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: ColorManager.textFieldColor(context),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        context.tr.hide,
                        style: context.labelMedium.copyWith(
                          color: context.appTheme.primaryColorDark,
                        ),
                      ),
                    ),
                  ),
                ),
                context.smallGap,
                Expanded(
                  child: InkWell(
                    onTap: () {
                      context.to(MainOrderDetailsScreen(order: order));
                    },
                    child: Container(
                      height: 35.h,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: ColorManager.primaryColor,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        context.tr.showDetails,
                        style: context.labelMedium.copyWith(
                          color: context.appTheme.primaryColorDark,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
        onConfirmBtnTap: () {});
  }

  static void _showAwesomeNotification(RemoteMessage message) {
    AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: DateTime.now().millisecondsSinceEpoch.remainder(100000),
        channelKey:
            VendorModelHelper.currentVendorModel().config?.orderRingingBell ==
                    true
                ? 'sound_channel'
                : 'basic_channel',
        customSound:
            VendorModelHelper.currentVendorModel().config?.orderRingingBell ==
                    true
                ? 'resource://raw/bell'
                : null,
        title: message.notification?.title ?? '',
        body: message.notification?.body ?? '',
        notificationLayout: NotificationLayout.Default,
        backgroundColor: ColorManager.black,
      ),
    );
  }

  static Future<void> showNotifications(BuildContext context) async {
    final orderVM = context.read<OrderVM>();

    FirebaseMessaging.onMessage.listen((message) async {
      final lastOrder = await orderVM.getOrders(context, limit: 1);

      _handleNotification(message, order: lastOrder.first);

      orderVM.getOrderStatistics(context);
      orderVM.getHomeOrders(context);
      orderVM.getOrders(context);
    });
  }

  // Get the FCM Token
  static Future<String> getToken() async {
    if (kIsWeb) return 'web';
    // if()

    final fcm = FirebaseMessaging.instance;

    if (UniversalPlatform.isApple && kDebugMode) {
      return 'apple';
    }

    final token = await fcm.getToken();
    return token ?? '';
  }

  // Subscribe to a specific topic
  static Future<void> subscribeToTopic(String topic) async {
    if (kIsWeb) return;
    if (UniversalPlatform.isApple && kDebugMode) {
      return;
    }
    final fcm = FirebaseMessaging.instance;
    log('SUBSCRIBED TO $topic');
    await fcm.subscribeToTopic(topic);
  }

  // Unsubscribe from a specific topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    if (kIsWeb) return;
    if (UniversalPlatform.isApple && kDebugMode) {
      return;
    }
    final fcm = FirebaseMessaging.instance;
    log('UNSUBSCRIBED FROM $topic');

    await fcm.unsubscribeFromTopic(topic);
  }

  // Send a notification via Firebase
  static Future<void> sendNotification(
      {required String title,
      required String body,
      required String userTokenOrTopic,
      bool isTopic = false}) async {
    const firebaseProjectId = 'idea2app-dev';

    const String fcmUrl =
        'https://fcm.googleapis.com/v1/projects/$firebaseProjectId/messages:send';

    Log.w('FCM_TOKEN_OR_TOPIC: $userTokenOrTopic');

    if (userTokenOrTopic.isEmpty) return;

    final accessToken = await AccessTokenFirebase().getAccessToken();

    Log.w('accessToken: $accessToken');

    final Map<String, dynamic> message = {
      'message': {
        'token': isTopic ? '/topics/$userTokenOrTopic' : userTokenOrTopic,
        'notification': {
          'title': title,
          'body': body,
        },
      }
    };

    final response = await http.post(
      Uri.parse(fcmUrl),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $accessToken',
      },
      body: jsonEncode(message),
    );

    if (response.statusCode == 200) {
      Log.w('NotificationSentSuccessfully ${response.body}');
    } else {
      Log.e('Failed to send notification: ${response.statusCode}');
      Log.e(response.body);
    }
  }
}

class AccessTokenFirebase {
  static const firebaseMessagingScope =
      'https://www.googleapis.com/auth/firebase.messaging';

  Future<String> getAccessToken() async {
    final jsonMap = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

    final client = await clientViaServiceAccount(
        ServiceAccountCredentials.fromJson(jsonMap), [firebaseMessagingScope]);

    final accessToken = client.credentials.accessToken.data;

    return accessToken;
  }
}

// import 'dart:convert';
// import 'dart:developer';
//
// import 'package:awesome_notifications/awesome_notifications.dart';
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart' show BuildContext;
// import 'package:googleapis_auth/auth_io.dart';
// import 'package:http/http.dart' as http;
// import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
// import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
//
// class NotificationService {
//   static Future<void> _firebaseMessagingBackgroundHandler(
//       RemoteMessage message) async {
//     if (Firebase.apps.isEmpty) await Firebase.initializeApp();
//     _showAwesomeNotification(message);
//   }
//
//   static Future<void> init() async {
//     final fcm = FirebaseMessaging.instance;
//
//     // Request permission for notifications
//     await fcm.requestPermission(
//       alert: true,
//       badge: true,
//       provisional: false,
//       sound: true,
//     );
//
//     // Set the options for notifications when the app is in the foreground
//     fcm.setForegroundNotificationPresentationOptions(
//       badge: true,
//       alert: true,
//       sound: true,
//     );
//
//     // Initialize Awesome Notifications
//     AwesomeNotifications().initialize(
//       null,
//       [
//         NotificationChannel(
//           channelKey: 'basic_channel',
//           channelName: 'Basic Notifications',
//           channelDescription: 'Notification channel for basic notifications',
//           defaultColor: ColorManager.primaryColor,
//           ledColor: ColorManager.primaryColor,
//           importance: NotificationImportance.High,
//         )
//       ],
//     );
//
//     // Set up Firebase Messaging background message handler
//     FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
//
//     if (kIsWeb) return;
//
//     fcm.subscribeToTopic('vendors');
//   }
//
//   // Show notifications with Awesome Notifications
//   static void _showAwesomeNotification(RemoteMessage message) {
//     AwesomeNotifications().createNotification(
//       content: NotificationContent(
//         id: DateTime.now().millisecondsSinceEpoch.remainder(100000),
//         channelKey: 'basic_channel',
//         title: message.notification?.title ?? '',
//         body: message.notification?.body ?? '',
//         notificationLayout: NotificationLayout.Default,
//       ),
//     );
//   }
//
//   // Listen for notifications when the app is open
//   static Future<void> showNotifications(BuildContext context) async {
//     FirebaseMessaging.onMessage.listen((message) {
//       final data = message.notification;
//       Log.w('onMessage: $data');
//
//       // Show Awesome Notification
//       _showAwesomeNotification(message);
//     });
//   }
//
//   // Get the FCM Token
//   static Future<String> getToken() async {
//     if (kIsWeb) return 'web';
//
//     final fcm = FirebaseMessaging.instance;
//     final token = await fcm.getToken();
//     return token ?? '';
//   }
//
//   // Subscribe to a specific topic
//   static Future<void> subscribeToTopic(String topic) async {
//     if (kIsWeb) return;
//     final fcm = FirebaseMessaging.instance;
//     log('SUBSCRIBED TO $topic');
//     await fcm.subscribeToTopic(topic);
//   }
//
//   // Unsubscribe from a specific topic
//   static Future<void> unsubscribeFromTopic(String topic) async {
//     if (kIsWeb) return;
//     final fcm = FirebaseMessaging.instance;
//     log('UNSUBSCRIBED FROM $topic');
//     await fcm.unsubscribeFromTopic(topic);
//   }
//
//   // Send a notification via Firebase
//   static Future<void> sendNotification(
//       {required String title,
//       required String body,
//       required String userTokenOrTopic,
//       bool isTopic = false}) async {
//     const firebaseProjectId = 'idea2app-dev';
//
//     const String fcmUrl =
//         'https://fcm.googleapis.com/v1/projects/$firebaseProjectId/messages:send';
//
//     Log.w('FCM_TOKEN_OR_TOPIC: $userTokenOrTopic');
//
//     if (userTokenOrTopic.isEmpty) return;
//
//     final accessToken = await AccessTokenFirebase().getAccessToken();
//
//     Log.w('accessToken: $accessToken');
//
//     final Map<String, dynamic> message = {
//       'message': {
//         'token': isTopic ? '/topics/$userTokenOrTopic' : userTokenOrTopic,
//         'notification': {
//           'title': title,
//           'body': body,
//         },
//       }
//     };
//
//     final response = await http.post(
//       Uri.parse(fcmUrl),
//       headers: {
//         'Content-Type': 'application/json; charset=UTF-8',
//         'Authorization': 'Bearer $accessToken',
//       },
//       body: jsonEncode(message),
//     );
//
//     if (response.statusCode == 200) {
//       Log.w('NotificationSentSuccessfully ${response.body}');
//     } else {
//       Log.e('Failed to send notification: ${response.statusCode}');
//       Log.e(response.body);
//     }
//   }
// }
//
// class AccessTokenFirebase {
//   static const firebaseMessagingScope =
//       'https://www.googleapis.com/auth/firebase.messaging';
//
//   Future<String> getAccessToken() async {
//     final jsonMap = {
//       "type": "service_account",
//       "project_id": "idea2app-dev",
//       "private_key_id": "61dad89510be63056a06feade9be12d94b8f8f5b",
//       "private_key":
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
//       "client_email":
//           "<EMAIL>",
//       "client_id": "109942368878149529175",
//       "auth_uri": "https://accounts.google.com/o/oauth2/auth",
//       "token_uri": "https://oauth2.googleapis.com/token",
//       "auth_provider_x509_cert_url":
//           "https://www.googleapis.com/oauth2/v1/certs",
//       "client_x509_cert_url":
//           "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-ad9jn%40idea2app-dev.iam.gserviceaccount.com",
//       "universe_domain": "googleapis.com"
//     };
//
//     final client = await clientViaServiceAccount(
//         ServiceAccountCredentials.fromJson(jsonMap), [firebaseMessagingScope]);
//
//     final accessToken = client.credentials.accessToken.data;
//
//     return accessToken;
//   }
// }
//
