import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:ip_country_lookup/ip_country_lookup.dart';
import 'package:ip_country_lookup/models/ip_country_data_model.dart';

class DefaultCountryService {
  static String currentCountry = 'Egypt';
  static String currentCurrency = 'EGP';

  static Future<String> getDefaultCurrency() async {
    final currencyPerCountry = await _getCurrencyBasedOnCountry();

    currentCurrency = currencyPerCountry;

    Log.w('Current_Country $currentCountry, Current_Currency $currentCurrency');

    return currencyPerCountry;
  }

  static Future<String> _getCurrentCountry() async {
    IpCountryData countryData = await IpCountryLookup().getIpLocationData();
    return countryData.country_name ?? '';
  }

  static Future<String> _getCurrencyBasedOnCountry() {
    return _getCurrentCountry()
        .timeout(const Duration(seconds: 10), onTimeout: () => 'Egypt')
        .catchError((_) => 'Egypt')
        .then((country) {
      currentCountry = country;
      switch (country) {
        case 'Saudi Arabia':
          return 'SAR';
        case 'Egypt':
          return 'EGP';
        case 'United Arab Emirates':
          return 'AED';
        default:
          return 'USD';
      }
    });
  }
}
